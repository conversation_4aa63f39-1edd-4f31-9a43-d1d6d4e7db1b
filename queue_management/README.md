# RabbitMQ Queue Management System

This module provides a robust RabbitMQ-based queue management system for job processing with the following features:

## Features

### 1. Connection Management
- Configurable RabbitMQ URL parameter
- Automatic connection retry logic (3 attempts with 5-second delays)
- Robust connection handling with heartbeat and timeout settings
- Graceful connection cleanup

### 2. Queue Configuration
- **Durable Queue**: Survives broker restarts
- **Maximum Length**: 50 messages maximum
- **Overflow Handling**: Rejects new messages when full (returns "service busy" message)
- **Message Persistence**: Messages survive broker restarts

### 3. Concurrency Control
- **Maximum Concurrent Jobs**: 20 simultaneous job processing
- **Prefetch Count**: Uses aio-pika's consumer prefetch_count for concurrency limiting
- **Queue Buffering**: Additional requests beyond 20 concurrent limit are queued

### 4. Integration with Existing System
- **execute_job_id Function**: New endpoint `/jobs/execute-queue/{job_id}` that uses the queue system
- **Backward Compatibility**: Original `/jobs/execute/{job_id}` endpoint remains unchanged
- **Seamless Integration**: Uses existing job processing logic and processors

### 5. Error Handling
- **Connection Retry Logic**: Automatic reconnection attempts
- **Queue Declaration Failures**: Proper error handling and logging
- **Job Processing Errors**: Failed jobs are marked appropriately in the database
- **Meaningful Error Messages**: Different failure scenarios return appropriate error messages

### 6. Async Context Management
- **Async with Support**: `async with AsyncRabbitMQ() as rabbitmq:`
- **Automatic Cleanup**: Connections are automatically closed when context exits
- **Resource Management**: Proper cleanup of channels and connections

## Usage

### 1. Basic Queue Operations

```python
from queue_management.async_rabbitmq import AsyncRabbitMQ, serialize_user_tenant_info

# Using async context manager (recommended)
async with AsyncRabbitMQ(amqp_url="amqp://guest:guest@rabbitmq/") as rabbitmq:
    # Publish a job
    user_data = serialize_user_tenant_info(user_tenant_info)
    success = await rabbitmq.publish_job(job_id, user_data)
    
    if not success:
        print("Queue is full - service busy")
    
    # Get queue information
    queue_info = await rabbitmq.get_queue_info()
    print(f"Queue has {queue_info['message_count']} messages")
```

### 2. Job Execution via Queue

```python
# POST /v1/jobs/execute-queue/{job_id}
# This endpoint will:
# 1. Validate the job
# 2. Queue it for processing
# 3. Return queue status information
# 4. Handle queue full scenarios gracefully
```

### 3. Queue Status Monitoring

```python
# GET /v1/jobs/queue/status
# Returns:
# - Current queue length
# - Number of active consumers
# - Capacity utilization percentages
# - Queue health status
```

### 4. Running the Consumer Service

```bash
# Start the consumer service to process jobs from the queue
python queue_management/consumer.py
```

## Configuration

### Environment Variables

```bash
# RabbitMQ connection URL
RABBITMQ_URL=amqp://guest:guest@rabbitmq/

# MongoDB connection (required for job processing)
MONGO_URI=mongodb://localhost:27017

# Other existing environment variables...
```

### Queue Parameters

The queue is configured with the following parameters:
- **Queue Name**: `job_processing_queue`
- **Max Length**: 50 messages
- **Max Concurrent Jobs**: 20
- **Overflow Behavior**: Reject new messages when full
- **Durability**: Queue and messages persist across broker restarts

## API Endpoints

### Execute Job via Queue
- **Endpoint**: `POST /v1/jobs/execute-queue/{job_id}`
- **Description**: Queue a job for processing with concurrency control
- **Responses**:
  - `200`: Job queued successfully
  - `503`: Queue is full (service busy)
  - `400`: Invalid job ID or unsupported process
  - `404`: Job not found

### Queue Status
- **Endpoint**: `GET /v1/jobs/queue/status`
- **Description**: Get current queue status and capacity information
- **Response**: Queue metrics and health status

## Error Scenarios

### Queue Full
When the queue reaches its maximum capacity (50 messages):
```json
{
    "message": "Please try again, service is busy",
    "job_id": "job_id_here",
    "status": "queue_full",
    "details": "Maximum queue capacity reached. Please try again later."
}
```

### RabbitMQ Unavailable
When RabbitMQ is not accessible:
```json
{
    "message": "Queue service unavailable. Please try again later."
}
```

## Deployment

### Docker Compose
The system is already configured to work with the existing Docker Compose setup:
- RabbitMQ service is defined in `docker-compose.yml`
- Default credentials: guest/guest
- Management UI available at: http://localhost:15672

### Consumer Service
Run the consumer service as a separate process:
```bash
# In production, use a process manager like systemd or supervisor
python queue_management/consumer.py
```

## Monitoring

### Queue Metrics
- Message count in queue
- Number of active consumers
- Capacity utilization percentages
- Processing throughput

### Logging
- All queue operations are logged
- Job processing status is tracked
- Error conditions are logged with details

## Benefits

1. **Scalability**: Handle high job volumes without overwhelming the system
2. **Reliability**: Jobs are persisted and won't be lost during system restarts
3. **Concurrency Control**: Prevent system overload with configurable limits
4. **Graceful Degradation**: Handle overload scenarios with meaningful user feedback
5. **Monitoring**: Real-time visibility into queue status and performance
6. **Backward Compatibility**: Existing functionality remains unchanged
