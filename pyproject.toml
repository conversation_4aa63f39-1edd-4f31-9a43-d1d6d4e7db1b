[project]
name = "backend-template"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "aio-pika>=9.4.3",
    "aiohttp>=3.12.15",
    "argon2-cffi>=23.1.0",
    "bcrypt>=4.3.0",
    "elevenlabs>=2.0.0",
    "fastapi[standard]>=0.115.11",
    "google-genai>=1.10.0",
    "google-generativeai>=0.8.4",
    "httpx>=0.28.1",
    "ipykernel>=6.30.1",
    "minio>=7.2.15",
    "pillow>=11.1.0",
    "pyjwt>=2.10.1",
    "pymongo>=4.11.2",
    "pytest>=8.3.5",
    "pytest-asyncio>=0.25.3",
    "pytest-cov>=6.0.0",
    "pytest-mongodb>=2.4.0",
    "python-dotenv>=1.0.1",
    "python-multipart>=0.0.20",
    "requests>=2.32.3",
    "uvicorn>=0.34.0",
]

[tool.pytest.ini_options]
pythonpath = "."
testpaths = ["app/v1/tests"]
python_files = "test_*.py"
addopts = "-v"
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
