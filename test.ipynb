{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e2aee6b1", "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "headers = {\n", "    'accept': 'application/json',\n", "    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjgyZGJhZWQ0YzVhNmZkNmQ3NjUwNzcwIiwiZXhwIjoxNzg2MDc5OTQzfQ.fdKa2prk_F__AQjRpJ6qIKh7YzU5r-SnXJIlU04C0Hc',\n", "    'Content-Type': 'application/x-www-form-urlencoded',\n", "}\n", "\n", "data = {\n", "    'text': 'Hello how are you what are you doing today brother',\n", "    'preset_voice': 'formal',\n", "    'user_voice_id': '6846a63e8c246ef2182ea246',\n", "    'quality': '20',\n", "}\n", "\n", "response = requests.post('https://tts-api.nextai.asia/v2/speak_v2', headers=headers, data=data)"]}, {"cell_type": "code", "execution_count": 3, "id": "d8934546", "metadata": {}, "outputs": [{"data": {"text/plain": ["'{\"message\":\"speak_v2 workflow completed successfully\",\"final_audio_url\":\"https://minio.nextai.asia/voice/speak_v2/1234323243/Bibek/speak_v2_output_a4a556c6-c423-40db-8031-626aaeccae29.wav?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minio-admin%2F20250806%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250806T053348Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=a180c6eb7319d6697695f5b3e45c6e142432d9c9c53163e6f328e3d002d93afa\",\"text\":\"hello how are you what are you doing today brother\",\"preset_voice\":\"formal\",\"user_voice_id\":\"6846a63e8c246ef2182ea246\",\"user_id\":\"1234323243\",\"user_name\":\"<PERSON>ibek\",\"final_file_size\":226618,\"created_at\":\"2025-08-06T05:33:48.082221\",\"minio_path\":\"speak_v2/1234323243/Bibek/speak_v2_output_a4a556c6-c423-40db-8031-626aaeccae29.wav\",\"workflow_steps\":{\"voice_generation_tts_size\":114332,\"voice_cloning_size\":0,\"timbre_transfer_size\":226618}}'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["response.text"]}, {"cell_type": "code", "execution_count": null, "id": "cb535248", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "backend-template (3.11.1)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.1"}}, "nbformat": 4, "nbformat_minor": 5}