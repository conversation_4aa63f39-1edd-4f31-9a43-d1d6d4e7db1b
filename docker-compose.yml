
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8300:8300"
    env_file:
      - .env
    restart: unless-stopped
    depends_on:
      - rabbitmq
    networks:
      - backend
    labels:
      - "com.docker.compose.project=aroma-backend-v2"

  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    ports:
      - "5672:5672"       # RabbitMQ protocol (AMQP)
      - "15672:15672"     # RabbitMQ Management UI
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    restart: unless-stopped
    networks:
      - backend

  rabbitmq-consumer-1:
    build:
      context: .
      dockerfile: Dockerfile.consumer
    container_name: rabbitmq-consumer-1
    env_file:
      - .env
    environment:
      RABBITMQ_URL: amqp://guest:guest@rabbitmq/
    restart: unless-stopped
    depends_on:
      - rabbitmq
    networks:
      - backend
    labels:
      - "com.docker.compose.project=aroma-backend-v2"

  rabbitmq-consumer-2:
    build:
      context: .
      dockerfile: Dockerfile.consumer
    container_name: rabbitmq-consumer-2
    env_file:
      - .env
    environment:
      RABBITMQ_URL: amqp://guest:guest@rabbitmq/
    restart: unless-stopped
    depends_on:
      - rabbitmq
    networks:
      - backend
    labels:
      - "com.docker.compose.project=aroma-backend-v2"

networks:
  backend:
